import subprocess
import time

# 测试的设备列表
devices = [0, 1, 5, 8, 9, 12, 15, 23, 29, 30, 34, 35, 36, 38, 40, 41, 43, 45]

# 你要播放的测试音频文件名
wav_file = "wake.wav"

for device in devices:
    device_str = f"hw:1,0"
    print(f"\nTesting device {device_str}...")
    try:
        # 调用 aplay 播放
        result = subprocess.run(
            ["aplay", "-D", device_str, wav_file],
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            timeout=5
        )
        print(result.stdout.decode())
    except subprocess.TimeoutExpired:
        print(f"Device {device_str}: aplay timed out.")
    except Exception as e:
        print(f"Device {device_str}: Error occurred: {e}")
    
    time.sleep(2)
